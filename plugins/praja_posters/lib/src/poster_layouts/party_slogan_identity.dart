import 'package:flutter/material.dart';
import 'package:praja_posters/src/auto_size_text/auto_size_text.dart';
import 'package:praja_posters/src/extensions/poster_gradient_extension.dart';
import 'package:praja_posters/src/models/poster_badge.dart';
import 'package:praja_posters/src/models/poster_font_config.dart';
import 'package:praja_posters/src/models/poster_gradient.dart';
import 'package:praja_posters/src/ui_widgets/poster_flat_badge_ribbon.dart';
import 'package:praja_posters/src/ui_widgets/praja_poster_image.dart';
import 'package:praja_posters/src/utils/font_utils.dart';

const double _partyIconWidth = 78;
const double _partyIconHeight = 78;

class PartySloganIdentity extends StatelessWidget {
  final String name;
  final PosterBadge? badge;
  final PosterGradient footerGradients;
  final PosterGradient? badgeBannerGradients;
  final double minNameFontSize;
  final double maxNameFontSize;
  final double minBadgeTextFontSize;
  final double maxBadgeTextFontSize;
  final int nameTextColor;
  final int badgeTextColor;
  final String? partyIcon;
  final String sloganText;
  final int? borderHighlightColor;
  final PosterFontConfig? nameFontConfig;
  final PosterFontConfig? badgeFontConfig;
  final bool showBadgeRibbon;

  const PartySloganIdentity({
    super.key,
    required this.name,
    this.badge,
    required this.footerGradients,
    this.badgeBannerGradients,
    this.minNameFontSize = 14,
    this.maxNameFontSize = 22,
    this.minBadgeTextFontSize = 9,
    this.maxBadgeTextFontSize = 11,
    required this.nameTextColor,
    required this.badgeTextColor,
    this.partyIcon,
    required this.sloganText,
    this.borderHighlightColor,
    this.nameFontConfig,
    this.badgeFontConfig,
    required this.showBadgeRibbon,
  });

  Widget _buildNameWidget() {
    final nameBrightness =
        ThemeData.estimateBrightnessForColor(Color(nameTextColor));
    return AutoSizeText(
      name,
      maxLines: 1,
      textAlign: TextAlign.center,
      minFontSize: minNameFontSize,
      maxFontSize: maxNameFontSize,
      textScaler: const TextScaler.linear(1.0),
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        shadows: [
          Shadow(
            color: nameBrightness == Brightness.light
                ? Colors.black
                : Colors.white,
            offset: const Offset(-2, 0),
            blurRadius: 0,
          ),
        ],
        color: Color(nameTextColor),
        fontSize: maxNameFontSize,
        fontWeight: FontWeight.bold,
        fontFamily: FontUtils.getFontFamily(fontConfig: nameFontConfig),
      ),
    );
  }

  Widget _buildBadgeRoleWidget() {
    final badge = this.badge;
    if (badge == null || !badge.active || badge.description.isEmpty) {
      return const SizedBox();
    }
    return Padding(
      padding: const EdgeInsets.only(top: 5),
      child: AutoSizeText(
        badge.description,
        maxLines: 1,
        textAlign: TextAlign.center,
        minFontSize: minBadgeTextFontSize,
        maxFontSize: maxBadgeTextFontSize,
        textScaler: const TextScaler.linear(1.0),
        overflow: TextOverflow.ellipsis,
        style: TextStyle(
          color: Color(badgeTextColor),
          fontSize: maxBadgeTextFontSize,
          fontWeight: FontWeight.bold,
          fontFamily: FontUtils.getFontFamily(fontConfig: badgeFontConfig),
        ),
      ),
    );
  }

  Widget _buildRibbonWidget() {
    final badge = this.badge;
    final showBadgeStrip =
        badge != null && badge.active && badge.description.isNotEmpty;
    final badgeBannerGradients = this.badgeBannerGradients;
    if (badgeBannerGradients == null) {
      return const SizedBox();
    }
    return showBadgeStrip
        ? Padding(
            padding: const EdgeInsets.only(top: 5),
            child: PosterFlatBadgeRibbon(
              text: badge.description,
              outlineType: badge.badgeBanner,
              backgroundGradient: badgeBannerGradients,
              minBadgeTextFontSize: minBadgeTextFontSize,
              maxBadgeTextFontSize: maxBadgeTextFontSize,
              badgeTextColor: badgeTextColor,
              badgeFontConfig: badgeFontConfig,
            ),
          )
        : const SizedBox();
  }

  Widget _buildPartyIconWithSloganWidget() {
    final partyIcon = this.partyIcon;
    final borderHighlightColor = this.borderHighlightColor;
    if ((partyIcon == null && sloganText.isEmpty) ||
        borderHighlightColor == null) {
      return const SizedBox();
    }
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          width: double.infinity,
          height: 70,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(50),
            border: Border.all(
              color: Color(borderHighlightColor),
              width: 4,
            ),
          ),
          padding: EdgeInsets.only(
            left: (partyIcon != null ? (_partyIconWidth - 16) : 10),
            right: 10,
          ),
          alignment: Alignment.center,
          child: AutoSizeText(
            sloganText,
            maxLines: 2,
            textAlign: TextAlign.center,
            minFontSize: 7,
            maxFontSize: 18,
            textScaler: const TextScaler.linear(1.0),
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              color: Color(nameTextColor),
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        if (partyIcon != null) ...[
          Positioned(
            left: -10,
            top: -5,
            child: PrajaPosterImage(
              height: _partyIconHeight,
              width: _partyIconWidth,
              imageUrl: partyIcon,
              placeholder: (context, url) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              },
            ),
          )
        ]
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final badge = this.badge;
    final hasBadge =
        badge != null && badge.active && badge.description.isNotEmpty;
    return Container(
      height: 98,
      padding: const EdgeInsets.only(left: 20, right: 20, top: 4, bottom: 2),
      decoration: BoxDecoration(
        gradient: footerGradients.toGradient(),
      ),
      child: Row(
        mainAxisAlignment: partyIcon != null
            ? MainAxisAlignment.spaceBetween
            : MainAxisAlignment.center,
        children: [
          Expanded(
            flex: (partyIcon != null || sloganText.isNotEmpty) ? 55 : 100,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  flex: hasBadge ? 62 : 100,
                  child: Center(child: _buildNameWidget()),
                ),
                Expanded(
                  flex: hasBadge ? 38 : 0,
                  child: Center(
                    child: showBadgeRibbon
                        ? _buildRibbonWidget()
                        : _buildBadgeRoleWidget(),
                  ),
                ),
              ],
            ),
          ),
          if ((partyIcon != null || sloganText.isNotEmpty) &&
              borderHighlightColor != null) ...[
            const SizedBox(
              width: 20,
            ),
            Expanded(
              flex: 45,
              child: _buildPartyIconWithSloganWidget(),
            ),
          ],
        ],
      ),
    );
  }
}
