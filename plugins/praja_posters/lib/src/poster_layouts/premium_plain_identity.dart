import 'package:praja_posters/src/auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:praja_posters/src/constants.dart';
import 'package:praja_posters/src/extensions/poster_gradient_extension.dart';
import 'package:praja_posters/src/models/poster_badge.dart';
import 'package:praja_posters/src/models/poster_font_config.dart';
import 'package:praja_posters/src/models/poster_gradient.dart';
import 'package:praja_posters/src/ui_widgets/praja_poster_image.dart';
import 'package:praja_posters/src/utils/font_utils.dart';

const double _partyIconWidth = 115;
const double _partyIconHeight = 86;

/// Supports [PosterIdentityType.plainIdentity], [PosterIdentityType.plainIdentityWithPartyIcon]
class PremiumPlainIdentity extends StatelessWidget {
  final String name;
  final PosterBadge? badge;
  final PosterGradient footerGradients;
  final double minNameFontSize;
  final double maxNameFontSize;
  final double minBadgeTextFontSize;
  final double maxBadgeTextFontSize;
  final int nameTextColor;
  final int badgeTextColor;
  final String? partyIcon;
  final int? iconBackgroundColor;
  final bool isUserPositionBack;
  final PosterFontConfig? nameFontConfig;
  final PosterFontConfig? badgeFontConfig;

  const PremiumPlainIdentity({
    super.key,
    required this.name,
    this.badge,
    required this.footerGradients,
    this.minNameFontSize = 14,
    this.maxNameFontSize = 22,
    this.minBadgeTextFontSize = 9,
    this.maxBadgeTextFontSize = 11,
    required this.nameTextColor,
    required this.badgeTextColor,
    this.partyIcon,
    this.iconBackgroundColor,
    required this.isUserPositionBack,
    this.nameFontConfig,
    this.badgeFontConfig,
  });

  Widget _buildNameWidget() {
    final nameBrightness =
        ThemeData.estimateBrightnessForColor(Color(nameTextColor));
    return AutoSizeText(
      name,
      maxLines: 1,
      textAlign: TextAlign.center,
      minFontSize: minNameFontSize,
      maxFontSize: maxNameFontSize,
      textScaler: const TextScaler.linear(1.0),
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        shadows: [
          Shadow(
            color: nameBrightness == Brightness.light
                ? Colors.black
                : Colors.white,
            offset: const Offset(-2, 0),
            blurRadius: 0,
          ),
        ],
        color: Color(nameTextColor),
        fontSize: maxNameFontSize,
        fontWeight: FontWeight.bold,
        fontFamily: FontUtils.getFontFamily(fontConfig: nameFontConfig),
      ),
    );
  }

  Widget _buildBadgeRoleWidget() {
    final badge = this.badge;
    if (badge == null || !badge.active || badge.description.isEmpty) {
      return const SizedBox();
    }
    return Container(
      padding: const EdgeInsets.only(top: 5),
      alignment: Alignment.center,
      child: AutoSizeText(
        badge.description,
        maxLines: 1,
        textAlign: TextAlign.center,
        minFontSize: minBadgeTextFontSize,
        maxFontSize: maxBadgeTextFontSize,
        textScaler: const TextScaler.linear(1.0),
        overflow: TextOverflow.ellipsis,
        style: TextStyle(
          color: Color(badgeTextColor),
          fontSize: minBadgeTextFontSize +
              (maxBadgeTextFontSize - minBadgeTextFontSize) / 2,
          fontWeight: FontWeight.bold,
          fontFamily: FontUtils.getFontFamily(fontConfig: badgeFontConfig),
        ),
      ),
    );
  }

  Widget _partyIconContainer() {
    final iconBackgroundColor = this.iconBackgroundColor;
    final partyIcon = this.partyIcon;
    if (partyIcon == null) {
      return const SizedBox();
    }
    return Container(
      padding: const EdgeInsets.only(top: 2, left: 20, right: 40, bottom: 2),
      decoration: BoxDecoration(
        color: iconBackgroundColor != null ? Color(iconBackgroundColor) : null,
        borderRadius: BorderRadius.circular(40),
      ),
      child: PrajaPosterImage(
          height: _partyIconHeight,
          width: _partyIconWidth,
          imageUrl: partyIcon,
          alignment: Alignment.center,
          fadeInDuration: const Duration(milliseconds: 200),
          placeholder: (context, url) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }),
    );
  }

  Widget _getEmptySizedBox() {
    if (!isUserPositionBack) {
      return const SizedBox(width: posterUserImageWidth);
    } else if (partyIcon != null) {
      return const SizedBox(width: _partyIconWidth + 20);
    } else {
      return const SizedBox();
    }
  }

  @override
  Widget build(BuildContext context) {
    final badge = this.badge;
    final bool hasBadge =
        badge != null && badge.active && badge.description.isNotEmpty;
    return Stack(
      children: [
        Container(
          height: 98,
          padding: EdgeInsets.only(
            left: 20,
            right: 10,
            bottom: (hasBadge || partyIcon != null) ? 4 : 10,
            top: (hasBadge || partyIcon != null) ? 4 : 10,
          ),
          decoration: BoxDecoration(gradient: footerGradients.toGradient()),
          child: Row(
            mainAxisAlignment: partyIcon != null
                ? MainAxisAlignment.spaceBetween
                : MainAxisAlignment.center,
            children: [
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Expanded(
                      flex: hasBadge ? 65 : 100,
                      child: Center(child: _buildNameWidget()),
                    ),
                    Expanded(
                      flex: hasBadge ? 35 : 0,
                      child: _buildBadgeRoleWidget(),
                    ),
                  ],
                ),
              ),
              _getEmptySizedBox(),
            ],
          ),
        ),
        Positioned(
          right: -32,
          bottom: -2,
          child: _partyIconContainer(),
        )
      ],
    );
  }
}
