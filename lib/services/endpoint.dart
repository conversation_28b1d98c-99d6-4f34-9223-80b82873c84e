import 'dart:core';

import 'package:praja/enums/app_environment.dart';

class Endpoint {
  // api urls
  static const String prodRorBaseUrl = 'https://preprod-api.thecircleapp.in';
  static const String preProdRorBaseUrl = 'https://preprod-api.thecircleapp.in';

  static const String prodMessagingServiceUrl =
      'https://messaging-service-api.thecircleapp.in';
  static const String preProdMessagingServiceUrl =
      'https://messaging-service-preprod-api.thecircleapp.in';

  static const String localRorApiNgrokUrl = String.fromEnvironment(
    'LOCAL_ROR_NGROK_URL',
    defaultValue: '',
  );
  static const String localMessagingApiNgrokUrl = String.fromEnvironment(
    'LOCAL_MESSAGING_NGROK_URL',
    defaultValue: '',
  );

  static const String localIp =
      String.fromEnvironment('LOCAL_IP', defaultValue: '********');

  static const String prefix = '';

  // mobile web urls
  static const String prodWebHost = 'm.praja.buzz';
  static const String preProdWebHost = 'm.praja.buzz';
  static const String localWebHost = localRorApiNgrokUrl;

  static String getMessageServiceBaseUrl() {
    switch (AppEnvironment.current()) {
      case AppEnvironment.production:
        return Endpoint.prodMessagingServiceUrl;
      case AppEnvironment.preProd:
        return Endpoint.preProdMessagingServiceUrl;
      case AppEnvironment.local:
        if (localRorApiNgrokUrl.isEmpty) {
          return "http://${Endpoint.localIp}:3001";
        }
        return Endpoint.localMessagingApiNgrokUrl;
      default:
        return Endpoint.prodMessagingServiceUrl;
    }
  }

  static String getRorApiBaseUrl() {
    switch (AppEnvironment.current()) {
      case AppEnvironment.production:
        return Endpoint.prodRorBaseUrl;
      case AppEnvironment.preProd:
        return Endpoint.preProdRorBaseUrl;
      case AppEnvironment.local:
        if (localRorApiNgrokUrl.isEmpty) {
          return "http://${Endpoint.localIp}:3000";
        }
        return Endpoint.localRorApiNgrokUrl;
      default:
        return Endpoint.prodRorBaseUrl;
    }
  }

  static String getMobileWebUrl() {
    String apiScheme = 'https';
    String apiHost = prodWebHost;
    int? port;

    switch (AppEnvironment.current()) {
      case AppEnvironment.local:
        apiScheme = 'https';
        apiHost = localWebHost;
        port = null;
        break;

      case AppEnvironment.preProd:
        apiScheme = 'https';
        apiHost = preProdWebHost;
        port = null;
        break;

      case AppEnvironment.production:
        apiScheme = 'https';
        apiHost = prodWebHost;
        port = null;
        break;
    }

    return "$apiScheme://$apiHost${port != null ? ":$port" : ""}";
  }
}
