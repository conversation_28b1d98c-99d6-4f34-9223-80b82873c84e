import 'dart:io';

import 'package:praja/enums/poster_share_destination.dart';
import 'package:praja/features/whatsapp_share/whatsapp_share.dart';
import 'package:praja/utils/logger.dart';
import 'package:share_plus/share_plus.dart';

/// Helper class for video poster carousel sharing functionality
class VideoPosterCarouselHelper {
  /// Get appropriate Telugu text based on action method
  static String getActionText(PosterShareDestination actionMethod) {
    switch (actionMethod) {
      case PosterShareDestination.download:
        return 'కొన్ని క్షణాల్లో మీ గ్యాలరీలో సేవ్ అవుతుంది.';
      case PosterShareDestination.whatsapp:
        return 'కొన్ని క్షణాల్లో WhatsAppలో పంచుకోవచ్చు.';
      case PosterShareDestination.externalShare:
        return 'కొన్ని క్షణాల్లో మీకు ఇష్టమైన యాప్‌లో పంచుకోవచ్చు.';
      default:
        // Default fallback text when action method is not set
        return 'కొన్ని క్షణాల్లో మీ గ్యాలరీలో సేవ్ అవుతుంది.';
    }
  }

  /// Get appropriate Telugu success text based on action method
  static String getSuccessText(PosterShareDestination? actionMethod) {
    switch (actionMethod) {
      case PosterShareDestination.download:
        return 'వీడియో మీ గ్యాలరీలో సేవ్ అయింది.';
      case PosterShareDestination.whatsapp:
        return 'వీడియో డౌన్‌లోడ్ పూర్తైంది. \nఇప్పుడు WhatsAppలో పంచుకోండి.';
      case PosterShareDestination.externalShare:
        return 'వీడియో డౌన్‌లోడ్ పూర్తైంది. \nఇప్పుడు ఇతర యాప్స్ ద్వారా పంచుకోండి.';
      default:
        // Default fallback text when action method is not set
        return 'వీడియో మీ గ్యాలరీలో సేవ్ అయింది.';
    }
  }

  /// Handle sharing with destination-specific logic
  /// Only works for Whatsapp and generic sharing
  static Future<void> handleShare(
    String filePath,
    String shareText,
    PosterShareDestination destination,
  ) async {
    try {
      switch (destination) {
        case PosterShareDestination.whatsapp:
          await _handleWhatsAppShare(filePath, shareText);
          break;
        case PosterShareDestination.externalShare:
          await _handleGenericShare(filePath, shareText);
          break;
        case PosterShareDestination.download:
          // No specific handling needed for download
          // The Video will be downloaded to the gallery via BGDownloader
          break;
        case PosterShareDestination.unknown:
          await _handleGenericShare(filePath, shareText);
          break;
      }
    } catch (e) {
      printDebug('Share failed for $destination: $e');
      // Fallback to generic sharing if specific sharing fails
      if (destination != PosterShareDestination.download) {
        await _fallbackToGenericShare(filePath, shareText);
      }
    }
  }

  /// Handle WhatsApp sharing with platform-specific logic
  static Future<void> _handleWhatsAppShare(
      String filePath, String shareText) async {
    if (Platform.isIOS) {
      // For iOS, use generic sharing as WhatsApp might not be available
      await _handleGenericShare(filePath, shareText);
    } else {
      // For Android, use WhatsApp-specific sharing
      await WhatsappShareAndroid.shareFiles([XFile(filePath)], text: shareText);
    }
  }

  /// Handle generic sharing for all platforms
  static Future<void> _handleGenericShare(
      String filePath, String shareText) async {
    await Share.shareXFiles([XFile(filePath)], text: shareText);
  }

  /// Fallback to generic sharing when WhatsApp sharing fails
  static Future<void> _fallbackToGenericShare(
      String filePath, String shareText) async {
    try {
      await _handleGenericShare(filePath, shareText);
      printDebug('Successfully fell back to generic sharing');
    } catch (fallbackError) {
      printDebug('Fallback share also failed: $fallbackError');
      rethrow;
    }
  }
}
