<!-- Copyright 2014 The Flutter Authors. All rights reserved.
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file. -->

<resources>
    <style name="MyButton" parent="Widget.AppCompat.Button.Borderless">
        <item name="colorControlHighlight">@color/grey</item>
        <item name="colorButtonNormal">@color/white</item>
        <item name="android:textColor">@color/black</item>
    </style>
</resources>
