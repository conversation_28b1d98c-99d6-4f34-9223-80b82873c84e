#ifndef FLUTTER_PLUGIN_{{pluginClassCapitalSnakeCase}}_H_
#define FLUTTER_PLUGIN_{{pluginClassCapitalSnakeCase}}_H_

#include <flutter_linux/flutter_linux.h>

G_BEGIN_DECLS

#ifdef FLUTTER_PLUGIN_IMPL
#define FLUTTER_PLUGIN_EXPORT __attribute__((visibility("default")))
#else
#define FLUTTER_PLUGIN_EXPORT
#endif

typedef struct _{{pluginClass}} {{pluginClass}};
typedef struct {
  GObjectClass parent_class;
} {{pluginClass}}Class;

FLUTTER_PLUGIN_EXPORT GType {{pluginClassSnakeCase}}_get_type();

FLUTTER_PLUGIN_EXPORT void {{pluginClassSnakeCase}}_register_with_registrar(
    FlPluginRegistrar* registrar);

G_END_DECLS

#endif  // FLUTTER_PLUGIN_{{pluginClassCapitalSnakeCase}}_H_
